<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WaiverCheckIn</title>
    <base href="/" />
    <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="css/app.css" />
    <link rel="stylesheet" href="css/site.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="WaiverCheckIn.styles.css" rel="stylesheet" />
</head>

<body>
    <div id="app">
        <svg class="loading-progress">
            <circle r="40%" cx="50%" cy="50%" />
            <circle r="40%" cx="50%" cy="50%" />
        </svg>
        <div class="loading-progress-text"></div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
    <script src="_framework/blazor.webassembly.js"></script>
    <script>
        window.captureFormScreen = async function() {
            try {
                // Use html2canvas library to capture the form
                const formElement = document.querySelector('.checkin-container');
                if (!formElement) {
                    console.error('Form container not found');
                    return '';
                }

                // Load html2canvas dynamically if not already loaded
                if (typeof html2canvas === 'undefined') {
                    await loadHtml2Canvas();
                }

                // Capture the form area
                const canvas = await html2canvas(formElement, {
                    useCORS: true,
                    allowTaint: true,
                    scale: 1,
                    scrollX: 0,
                    scrollY: 0,
                    width: formElement.scrollWidth,
                    height: formElement.scrollHeight
                });

                // Convert to base64 string
                return canvas.toDataURL('image/png');
            } catch (error) {
                console.error('Error capturing screen:', error);
                return '';
            }
        };

        function loadHtml2Canvas() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
    </script>
</body>

</html>
