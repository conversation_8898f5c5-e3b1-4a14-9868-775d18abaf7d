<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Job Shadow Waiver - Check-In</title>
    <base href="/" />
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="css/app.css" />
    <link rel="stylesheet" href="css/site.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="JSWaiverCheckIn.Client.styles.css" rel="stylesheet" />
</head>

<body>
    <div id="app">
        <svg class="loading-progress">
            <circle r="40%" cx="50%" cy="50%" />
            <circle r="40%" cx="50%" cy="50%" />
        </svg>
        <div class="loading-progress-text"></div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
    <script src="_framework/blazor.webassembly.js"></script>
    <script>
        window.captureFormScreen = async function() {
            try {
                // Use html2canvas library to capture the form
                const formElement = document.querySelector('.checkin-container');
                if (!formElement) {
                    console.error('Form container not found');
                    return '';
                }

                // Load html2canvas dynamically if not already loaded
                if (typeof html2canvas === 'undefined') {
                    await loadHtml2Canvas();
                }

                // Capture the form area
                const canvas = await html2canvas(formElement, {
                    useCORS: true,
                    allowTaint: true,
                    scale: 1,
                    scrollX: 0,
                    scrollY: 0,
                    width: formElement.scrollWidth,
                    height: formElement.scrollHeight
                });

                // Convert to base64 string
                return canvas.toDataURL('image/png');
            } catch (error) {
                console.error('Error capturing screen:', error);
                return '';
            }
        };

        function loadHtml2Canvas() {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js';
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        window.scrollToNextCard = function(currentCardId) {
            //try {
            //    // Extract card number from current card ID (e.g., "card-1" -> 1)
            //    const currentCardNumber = parseInt(currentCardId.split('-')[1]);
            //    const nextCardNumber = currentCardNumber + 1;
            //    const nextCardId = `card-${nextCardNumber}`;

            //    // Find the next card element
            //    const nextCard = document.getElementById(nextCardId);

            //    if (nextCard) {
            //        // Smooth scroll to the next card with some offset for better visibility
            //        const offset = 80; // Adjust this value as needed
            //        const elementPosition = nextCard.getBoundingClientRect().top;
            //        const offsetPosition = elementPosition + window.pageYOffset - offset;

            //        window.scrollTo({
            //            top: offsetPosition,
            //            behavior: 'smooth'
            //        });

            //        // Add a subtle highlight effect to the next card
            //        nextCard.classList.add('card-highlight');
            //        setTimeout(() => {
            //            nextCard.classList.remove('card-highlight');
            //        }, 2000);

            //        return true;
            //    } else {
            //        // No next card found (probably the last card)
            //        return false;
            //    }
            //} catch (error) {
            //    console.error('Error scrolling to next card:', error);
            //    return false;
            //}
        };
    </script>
</body>

</html>
