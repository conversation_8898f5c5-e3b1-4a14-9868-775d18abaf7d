using Azure.Core;
using Azure.Identity;
using Microsoft.Graph;
using Microsoft.AspNetCore.ResponseCompression;

var builder = WebApplication.CreateBuilder(args);

// Graph client (app-only)
builder.Services.AddSingleton(sp =>
{
    var cfg = sp.GetRequiredService<IConfiguration>().GetSection("SharePoint");
    var tenantId = cfg["TenantId"]!;
    var clientId = cfg["ClientId"]!;
    var clientSecret = cfg["ClientSecret"]; // if null and running in Azure, you can use Managed Identity
    var baseUrl = $"{cfg["BaseUrl"] ?? "https://graph.microsoft.com"}/{cfg["Version"] ?? "v1.0"}";

    // Choose credential
    TokenCredential credential = !string.IsNullOrWhiteSpace(clientSecret)
        ? new ClientSecretCredential(tenantId, clientId, clientSecret!)
        // If you're using a *system-assigned* managed identity, use new ManagedIdentityCredential() with no clientId.
        : new ManagedIdentityCredential(clientId: clientId); // for user-assigned MI; omit clientId for system-assigned


    // App-only uses the ".default" scope
    var scopes = new[] { $"{cfg["BaseUrl"] ?? "https://graph.microsoft.com"}/.default" };
    return new GraphServiceClient(credential, scopes, baseUrl);
});

// optional: allow big form uploads (adjust as needed)
builder.Services.Configure<Microsoft.AspNetCore.Http.Features.FormOptions>(o =>
{
    o.MultipartBodyLengthLimit = 512L * 1024 * 1024; // 512 MB
});


builder.Services.AddControllersWithViews();
builder.Services.AddRazorPages();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
}
else
{
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseBlazorFrameworkFiles();
app.UseStaticFiles();

app.UseRouting();

app.MapRazorPages();
app.MapControllers();
app.MapFallbackToFile("index.html");

app.Run();
