using Azure.Identity;
using Microsoft.Graph;
using Microsoft.AspNetCore.ResponseCompression;

var builder = WebApplication.CreateBuilder(args);

// Graph client (app-only)
builder.Services.AddSingleton(sp =>
{
    var cfg = sp.GetRequiredService<IConfiguration>().GetSection("Graph");
    var tenantId = cfg["SharePoint:TenantId"]!;
    var clientId = cfg["SharePoint:ClientId"]!;
    var clientSecret = cfg["SharePoint:ClientSecret"]; // if null and running in Azure, you can use Managed Identity
    var baseUrl = $"{cfg["SharePoint:BaseUrl"] ?? "https://graph.microsoft.com"}/{cfg["Version"] ?? "v1.0"}";

    var credential = clientSecret is not null
        ? new ClientSecretCredential(tenantId, clientId, clientSecret)
        : new ManagedIdentityCredential(clientId: clientId);

    // App-only uses the ".default" scope
    var scopes = new[] { $"{cfg["BaseUrl"] ?? "https://graph.microsoft.com"}/.default" };
    return new GraphServiceClient(credential, scopes, baseUrl);
});

// optional: allow big form uploads (adjust as needed)
builder.Services.Configure<Microsoft.AspNetCore.Http.Features.FormOptions>(o =>
{
    o.MultipartBodyLengthLimit = 512L * 1024 * 1024; // 512 MB
});


builder.Services.AddControllersWithViews();
builder.Services.AddRazorPages();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseWebAssemblyDebugging();
}
else
{
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseBlazorFrameworkFiles();
app.UseStaticFiles();

app.UseRouting();

app.MapRazorPages();
app.MapControllers();
app.MapFallbackToFile("index.html");

app.Run();
