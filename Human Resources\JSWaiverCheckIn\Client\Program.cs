using Blazorise;
using Blazorise.Bootstrap;
using JSWaiverCheckIn.Client;
using JSWaiverCheckIn.Client.Services;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

builder.Services.AddHttpClient("JSWaiverCheckIn.ServerAPI", client => client.BaseAddress = new Uri(builder.HostEnvironment.BaseAddress));

// Supply HttpClient instances that include access tokens when making requests to the server project
builder.Services.AddScoped(sp => sp.GetRequiredService<IHttpClientFactory>().CreateClient("JSWaiverCheckIn.ServerAPI"));

// Register upload service
builder.Services.AddScoped<UploadService>();

builder.Services
               .AddBlazorise(options =>
               {
                   options.Immediate = true;
               })
               .AddBootstrapProviders();


await builder.Build().RunAsync();
