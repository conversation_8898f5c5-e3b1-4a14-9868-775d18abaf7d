/* Global Styles */
body {
    background: linear-gradient(135deg, #1f1f1f 0%, #0a0a0a 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

html {
    scroll-behavior: smooth;
}

/* Landing Page Styles */
.landing-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
    text-align: center;
    padding: 2rem;
}

.logo-container {
    margin-bottom: 2rem;
}

.logo-placeholder {
    width: 200px;
    height: 100px;
    background-color: #f0f0f0;
    border: 2px dashed #ccc;
    border-radius: 8px;
    object-fit: contain;
}

.title-container {
    margin-bottom: 3rem;
    color: #333;
}

.main-title {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.button-container {
    margin-top: 1rem;
}

.check-in-button {
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: bold;
}

/* Check-In Form Styles */
.checkin-container {
    max-width: 700px;
    margin: 2rem auto;
    padding: 2rem;
    background-color: #000000;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-title {
    text-align: center;
    margin-bottom: 2rem;
    color: #ffffff;
}

.form-card {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-title {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
    border-bottom: 2px solid #7e132a;
    padding-bottom: 0.5rem;
}

.checkin-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 600;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    color: #555;
}

.form-control {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check-input {
    /*width: auto;*/
    margin: 0;
}

    .form-check-input:checked {
        background-color: #7e132a;
        border-color: #7e132a;
    }

.form-check-label {
    margin: 0;
    font-weight: normal;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    font-weight: bold;
}

.btn {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    text-align: center;

    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(126, 19, 42, 0.4);
}

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(126, 19, 42, 0.4);
    }

.btn-clear {
    box-shadow: none !important;
}

.btn-primary {
    background-color: #7e132a;
    border: 1px solid #7e132a;
    color: white;
}

    .btn-primary:hover, .btn-primary:active {
        background-color: #7e132a;
        border: 1px solid #7e132a;
    }

.btn-secondary {
    background-color: #000000;
    color: white;
}

    .btn-secondary:hover, .btn-secondary:active {
        background-color: #000000;
    }

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.validation-message {
    color: red;
    font-size: small;
}


/* Button Group Styles */
.button-group {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.button-group .btn {
    /*flex: 1;*/
    min-width: 120px;
}

.btn-outline-primary {
    background-color: transparent;
    border: 1px solid #7e132a;
    color: #7e132a;
}

    .btn-outline-primary:hover {
        background-color: #7e132a;
        border: 1px solid #7e132a;
        color: white;
    }

/* Signature Pad Styles */
.signature-container {
    width: 405px; /* Fixed Width, based on Blazorise SignaturePad */
    position: relative;
    border: 2px solid #ddd;
    border-radius: 4px;
    background-color: #ffffff;
    margin-top: 0.5rem;
}

.signature-pad {
    width: 100%;
    max-width: 400px;
    height: 150px;
    border: none;
    border-radius: 4px;
    border-bottom: 0.5px solid #ddd;
}

.signature-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #999;
    font-style: italic;
    pointer-events: none;
    z-index: 1;
}

.signature-actions {
    text-align: right;
}

.date-display {
    padding: 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-weight: 600;
    color: #333;
    margin-top: 0.5rem;
}

/* See More/Less Functionality */
.confidentiality-text {
    position: relative;
}

.text-preview {
    max-height: 100px;
    overflow: hidden;
    position: relative;
}

.text-preview::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(transparent, white);
    pointer-events: none;
}

.see-more-btn {
    padding: 0.25rem 0;
    font-size: 0.9rem;
    text-decoration: none;
    color: #7e132a;
    border: none;
    background: none;
    cursor: pointer;
}

    .see-more-btn:hover {
        text-decoration: underline;
        color: #7e132a;
    }

.full-text {
    display: block;
}

/* Loading */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-content {
    background: white;
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 90%;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-content h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 1.5rem;
}

.loading-content p {
    color: #666;
    margin: 0;
    font-size: 1rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Card highlight effect for auto-scroll */
.card-highlight {
    transform: scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3) !important;
    border: 2px solid rgba(0, 123, 255, 0.5) !important;
    transition: all 0.3s ease;
}

/* Thank you Page */

.thank-you-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1f1f1f 0%, #0a0a0a 100%);
    padding: 20px;
}

.thank-you-content {
    background: white;
    border-radius: 20px;
    padding: 60px 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
}

.success-icon {
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
}

.thank-you-title {
    color: #333;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.thank-you-message {
    margin-bottom: 40px;
}

    .thank-you-message p {
        color: #666;
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 15px;
    }

.thank-you-actions {
    margin-top: 30px;
}

/* Print Settings */
@media print {
    .no-print {
        display: none !important;
    }
}

/* Responsiveness */
@media (max-width: 768px) {
    .thank-you-content {
        padding: 40px 20px;
    }

    .thank-you-title {
        font-size: 2rem;
    }

    .thank-you-message p {
        font-size: 1rem;
    }
}
