/* Global Styles */
body {
    background: linear-gradient(135deg, #3a3a3a 0%, #0f0f0f 60%, #000000 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Landing Page Styles */
.landing-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 80vh;
    text-align: center;
    padding: 2rem;
}

.logo-container {
    margin-bottom: 2rem;
}

.logo-placeholder {
    width: 200px;
    height: 100px;
    background-color: #f0f0f0;
    border: 2px dashed #ccc;
    border-radius: 8px;
    object-fit: contain;
}

.title-container {
    margin-bottom: 3rem;
    color: #333;
}

.main-title {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
    margin: 0;
}

.button-container {
    margin-top: 1rem;
}

.check-in-button {
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: bold;
}

/* Check-In Form Styles */
.checkin-container {
    max-width: 700px;
    margin: 2rem auto;
    padding: 2rem;
    background-color: #000000;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-title {
    text-align: center;
    margin-bottom: 2rem;
    color: #ffffff;
}

.form-card {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-title {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
    border-bottom: 2px solid #7e132a;
    padding-bottom: 0.5rem;
}

.checkin-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 600;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    color: #555;
}

.form-control {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-check-input {
    /*width: auto;*/
    margin: 0;
}

    .form-check-input:checked {
        background-color: #7e132a;
        border-color: #7e132a;
    }

.form-check-label {
    margin: 0;
    font-weight: normal;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    font-weight: bold;
}

.btn {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: #7e132a;
    border: 1px solid #7e132a;
    color: white;
}

    .btn-primary:hover, .btn-primary:active {
        background-color: #7e132a;
        border: 1px solid #7e132a;
    }

.btn-secondary {
    background-color: #000000;
    color: white;
}

    .btn-secondary:hover, .btn-secondary:active {
        background-color: #000000;
    }

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Button Group Styles */
.button-group {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 0.5rem;
}

.button-group .btn {
    /*flex: 1;*/
    min-width: 120px;
}

.btn-outline-primary {
    background-color: transparent;
    border: 1px solid #7e132a;
    color: #7e132a;
}

    .btn-outline-primary:hover {
        background-color: #7e132a;
        border: 1px solid #7e132a;
        color: white;
    }

/* Signature Pad Styles */
.signature-container {
    width: 405px; /* Fixed Width, based on Blazorise SignaturePad */
    position: relative;
    border: 2px solid #ddd;
    border-radius: 4px;
    background-color: #ffffff;
    margin-top: 0.5rem;
}

.signature-pad {
    width: 100%;
    max-width: 400px;
    height: 150px;
    border: none;
    border-radius: 4px;
    border-bottom: 0.5px solid #ddd;
}

.signature-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #999;
    font-style: italic;
    pointer-events: none;
    z-index: 1;
}

.signature-actions {
    text-align: right;
}

.date-display {
    padding: 0.75rem;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-weight: 600;
    color: #333;
    margin-top: 0.5rem;
}

/* See More/Less Functionality */
.confidentiality-text {
    position: relative;
}

.text-preview {
    max-height: 100px;
    overflow: hidden;
    position: relative;
}

.text-preview::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(transparent, white);
    pointer-events: none;
}

.see-more-btn {
    padding: 0.25rem 0;
    font-size: 0.9rem;
    text-decoration: none;
    color: #7e132a;
    border: none;
    background: none;
    cursor: pointer;
}

    .see-more-btn:hover {
        text-decoration: underline;
        color: #7e132a;
    }

.full-text {
    display: block;
}


/* Print Settings */
@media print {
    .no-print {
        display: none !important;
    }
}