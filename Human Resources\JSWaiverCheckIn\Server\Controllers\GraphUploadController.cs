using Microsoft.AspNetCore.Mvc;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Microsoft.Graph.Drives.Item.Items.Item.CreateUploadSession;

namespace JSWaiverCheckIn.Server.Controllers
{
    [ApiController]
    [Route("api/graph")]
    public class GraphUploadController : ControllerBase
    {
        private readonly GraphServiceClient _graph;
        private readonly ILogger<GraphUploadController> _logger;

        public GraphUploadController(GraphServiceClient graph, ILogger<GraphUploadController> logger)
        {
            _graph = graph;
            _logger = logger;
        }

        /// <summary>
        /// Uploads a file into a target folder driveItem.
        /// Query: driveId, parentItemId, fileName
        /// Body: multipart/form-data with "file"
        /// </summary>
        [HttpPost("upload")]
        [RequestSizeLimit(long.MaxValue)]
        public async Task<IActionResult> Upload(
            [FromQuery] string driveId,
            [FromQuery] string parentItemId,
            [FromQuery] string fileName,
            CancellationToken ct)
        {
            // Validate required parameters
            if (string.IsNullOrEmpty(driveId))
                return BadRequest("driveId is required.");
            if (string.IsNullOrEmpty(parentItemId))
                return BadRequest("parentItemId is required.");
            if (string.IsNullOrEmpty(fileName))
                return BadRequest("fileName is required.");

            if (!Request.HasFormContentType)
                return BadRequest("multipart/form-data required.");

            var form = await Request.ReadFormAsync(ct);
            var file = form.Files.FirstOrDefault();
            if (file is null || file.Length == 0)
                return BadRequest("No file provided.");

            try
            {
                _logger.LogInformation("Starting upload for file: {FileName}, Size: {FileSize} bytes", fileName, file.Length);

                await using var stream = file.OpenReadStream(long.MaxValue);

                // Small-file upload supports up to 250 MB via single PUT to /content
                const long SmallLimit = 250L * 1024 * 1024;

                if (file.Length <= SmallLimit)
                {
                    _logger.LogInformation("Using small file upload for {FileName}", fileName);
                    var created = await _graph
                        .Drives[driveId]
                        .Items[parentItemId]
                        .ItemWithPath(fileName)
                        .Content
                        .PutAsync<DriveItem>(stream, cancellationToken: ct);

                    _logger.LogInformation("Small file upload completed successfully for {FileName}", fileName);
                    return Ok(new
                    {
                        id = created?.Id,
                        name = created?.Name,
                        webUrl = created?.WebUrl,
                        size = created?.Size
                    });
                }
            else
            {
                _logger.LogInformation("Using large file upload for {FileName}", fileName);
                // Large file: create upload session then stream chunks (multiple of 320 KiB)
                var sessionBody = new CreateUploadSessionPostRequestBody
                {
                    Item = new DriveItemUploadableProperties
                    {
                        Name = fileName,
                        AdditionalData = new Dictionary<string, object>
                        {
                            ["@microsoft.graph.conflictBehavior"] = "replace"
                        }
                    }
                };

                var session = await _graph
                    .Drives[driveId]
                    .Items[parentItemId]
                    .CreateUploadSession
                    .PostAsync(sessionBody, cancellationToken: ct);

                if (session is null) return Problem("Failed to create upload session.");

                int chunkSize = 320 * 1024; // 320 KiB
                var task = new Microsoft.Graph.Upload.UploadSessionLargeFileUploadTask<DriveItem>(session, stream, chunkSize);
                var result = await task.UploadAsync(ct);

                if (result.UploadSucceeded && result.ItemResponse is not null)
                {
                    var item = result.ItemResponse;
                    return Ok(new { id = item.Id, name = item.Name, webUrl = item.WebUrl, size = item.Size });
                }

                return Problem("Upload failed.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file {FileName}", fileName);
                return Problem($"Upload error: {ex.Message}");
            }
        }
    }
}
