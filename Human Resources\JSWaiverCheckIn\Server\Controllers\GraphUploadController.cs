﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Microsoft.Graph.Drives.Item.Items.Item.CreateUploadSession;

namespace JSWaiverCheckIn.Server.Controllers
{
    [ApiController]
    [Route("api/graph")]
    public class GraphUploadController : ControllerBase
    {
        private readonly GraphServiceClient _graph;
        private readonly ILogger<GraphUploadController> _logger;
        private readonly IConfiguration _configuration;

        public GraphUploadController(GraphServiceClient graph, ILogger<GraphUploadController> logger, IConfiguration configuration)
        {
            _graph = graph;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Uploads a file into a target folder driveItem.
        /// Query: driveId, parentItemId, fileName
        /// Body: multipart/form-data with "file"
        /// </summary>
        [HttpPost("upload")]
        [RequestSizeLimit(long.MaxValue)]
        public async Task<IActionResult> Upload(
            [FromQuery] string driveId,
            [FromQuery] string parentItemId,
            [FromQuery] string fileName,
            CancellationToken ct)
        {
            // Validate required parameters
            if (string.IsNullOrEmpty(driveId))
                return BadRequest("driveId is required.");
            if (string.IsNullOrEmpty(parentItemId))
                return BadRequest("parentItemId is required.");
            if (string.IsNullOrEmpty(fileName))
                return BadRequest("fileName is required.");

            if (!Request.HasFormContentType)
                return BadRequest("multipart/form-data required.");

            var form = await Request.ReadFormAsync(ct);
            var file = form.Files.Count > 0 ? form.Files[0] : null;
            if (file is null || file.Length == 0)
                return BadRequest("No file provided.");

            try
            {
                _logger.LogInformation("Starting upload for file: {FileName}, Size: {FileSize} bytes", fileName, file.Length);

                await using var stream = file.OpenReadStream();

                // Small-file upload supports up to 250 MB via single PUT to /content
                const long SmallLimit = 250L * 1024 * 1024;

                if (file.Length <= SmallLimit)
                {
                    _logger.LogInformation("Using small file upload for {FileName}", fileName);
                    var created = await _graph
                        .Drives[driveId]
                        .Items[parentItemId]
                        .ItemWithPath(fileName)
                        .Content
                        .PutAsync(stream, cancellationToken: ct);

                    _logger.LogInformation("Small file upload completed successfully for {FileName}", fileName);

                    // Get the created item details
                    var createdItem = await _graph
                        .Drives[driveId]
                        .Items[parentItemId]
                        .ItemWithPath(fileName)
                        .GetAsync(cancellationToken: ct);

                    return Ok(new
                    {
                        id = createdItem?.Id,
                        name = createdItem?.Name,
                        webUrl = createdItem?.WebUrl,
                        size = createdItem?.Size
                    });
                }
            else
            {
                _logger.LogInformation("Using large file upload for {FileName}", fileName);
                // Large file: create upload session then stream chunks (multiple of 320 KiB)
                var sessionBody = new CreateUploadSessionPostRequestBody
                {
                    Item = new DriveItemUploadableProperties
                    {
                        Name = fileName,
                        AdditionalData = new Dictionary<string, object>
                        {
                            ["@microsoft.graph.conflictBehavior"] = "replace"
                        }
                    }
                };

                var session = await _graph
                    .Drives[driveId]
                    .Items[parentItemId]
                    .CreateUploadSession
                    .PostAsync(sessionBody, cancellationToken: ct);

                if (session is null) return Problem("Failed to create upload session.");

                // For now, return an error for large files since we don't have the Upload package properly configured
                // Most screen captures should be under 250MB anyway
                return Problem("File too large. Maximum supported size is 250MB.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file {FileName}", fileName);
                return Problem($"Upload error: {ex.Message}");
            }
        }

        /// <summary>
        /// Uploads a screen capture to SharePoint using configuration from appsettings.
        /// Body: JSON with base64Data, candidateName, and timestamp
        /// </summary>
        [HttpPost("upload-screen-capture")]
        public async Task<IActionResult> UploadScreenCapture([FromBody] ScreenCaptureRequest request, CancellationToken ct)
        {
            try
            {
                _logger.LogInformation("Starting screen capture upload for candidate: {CandidateName}", request.CandidateName);

                // Get SharePoint configuration
                var driveId = _configuration["SharePoint:DriveId"];
                var folderId = _configuration["SharePoint:FolderId"];

                if (string.IsNullOrEmpty(driveId) || string.IsNullOrEmpty(folderId))
                {
                    _logger.LogError("SharePoint configuration missing - DriveId or FolderId not found");
                    return BadRequest("SharePoint configuration missing");
                }

                // Generate filename
                var fileName = $"CheckIn_{SanitizeFileName(request.CandidateName)}_{request.Timestamp:yyyyMMdd_HHmmss}.png";
                _logger.LogInformation("Generated filename: {FileName}", fileName);

                // Convert base64 to bytes
                var base64Content = request.Base64Data.Contains(",")
                    ? request.Base64Data.Substring(request.Base64Data.IndexOf(",") + 1)
                    : request.Base64Data;

                var fileBytes = Convert.FromBase64String(base64Content);
                _logger.LogInformation("File size: {FileSize} bytes", fileBytes.Length);

                // Create stream from bytes
                using var stream = new MemoryStream(fileBytes);

                // Upload to SharePoint
                const long SmallLimit = 250L * 1024 * 1024;
                if (fileBytes.Length > SmallLimit)
                {
                    return Problem("File too large. Maximum supported size is 250MB.");
                }

                _logger.LogInformation("Uploading to SharePoint...");
                await _graph
                    .Drives[driveId]
                    .Items[folderId]
                    .ItemWithPath(fileName)
                    .Content
                    .PutAsync(stream, cancellationToken: ct);

                // Get the created item details
                var createdItem = await _graph
                    .Drives[driveId]
                    .Items[folderId]
                    .ItemWithPath(fileName)
                    .GetAsync(cancellationToken: ct);

                _logger.LogInformation("Screen capture upload completed successfully for {FileName}", fileName);

                return Ok(new
                {
                    id = createdItem?.Id,
                    name = createdItem?.Name,
                    webUrl = createdItem?.WebUrl,
                    size = createdItem?.Size
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading screen capture for candidate {CandidateName}", request.CandidateName);
                return Problem($"Upload error: {ex.Message}");
            }
        }

        private static string SanitizeFileName(string fileName)
        {
            // Remove invalid characters for SharePoint file names
            var invalidChars = new char[] { '/', '\\', ':', '*', '?', '"', '<', '>', '|', '#', '%' };
            foreach (var invalidChar in invalidChars)
            {
                fileName = fileName.Replace(invalidChar, '_');
            }
            return fileName;
        }

        public class ScreenCaptureRequest
        {
            public string Base64Data { get; set; } = string.Empty;
            public string CandidateName { get; set; } = string.Empty;
            public DateTime Timestamp { get; set; }
        }
    }
}
