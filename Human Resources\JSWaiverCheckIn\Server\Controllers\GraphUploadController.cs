﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Microsoft.Graph.Drives.Item.Items.Item.CreateUploadSession;

namespace JSWaiverCheckIn.Server.Controllers
{
    [ApiController]
    [Route("api/graph")]
    public class GraphUploadController : ControllerBase
    {
        private readonly GraphServiceClient _graph;
        public GraphUploadController(GraphServiceClient graph) => _graph = graph;

        /// <summary>
        /// Uploads a file into a target folder driveItem.
        /// Query: driveId, parentItemId, fileName
        /// Body: multipart/form-data with "file"
        /// </summary>
        [HttpPost("upload")]
        [RequestSizeLimit(long.MaxValue)]
        public async Task<IActionResult> Upload(
            [FromQuery] string driveId,
            [FromQuery] string parentItemId,
            [FromQuery] string fileName,
            CancellationToken ct)
        {
            if (!Request.HasFormContentType) return BadRequest("multipart/form-data required.");
            var form = await Request.ReadFormAsync(ct);
            var file = form.Files.FirstOrDefault();
            if (file is null || file.Length == 0) return BadRequest("No file.");

            await using var stream = file.OpenReadStream(long.MaxValue);

            // Small-file upload supports up to 250 MB via single PUT to /content. :contentReference[oaicite:1]{index=1}
            const long SmallLimit = 250L * 1024 * 1024;

            if (file.Length <= SmallLimit)
            {
                var created = await _graph
                    .Drives[driveId]
                    .Items[parentItemId]
                    .ItemWithPath(fileName)
                    .Content
                    .PutAsync<DriveItem>(stream, cancellationToken: ct);

                return Ok(new
                {
                    id = created?.Id,
                    name = created?.Name,
                    webUrl = created?.WebUrl,
                    size = created?.Size
                });
            }
            else
            {
                // Large file: create upload session then stream chunks (multiple of 320 KiB). :contentReference[oaicite:2]{index=2}
                var sessionBody = new CreateUploadSessionPostRequestBody
                {
                    Item = new DriveItemUploadableProperties
                    {
                        Name = fileName,
                        AdditionalData = new Dictionary<string, object>
                        {
                            ["@microsoft.graph.conflictBehavior"] = "replace"
                        }
                    }
                };

                var session = await _graph
                    .Drives[driveId]
                    .Items[parentItemId]
                    .CreateUploadSession
                    .PostAsync(sessionBody, cancellationToken: ct);

                if (session is null) return Problem("Failed to create upload session.");

                int chunkSize = 320 * 1024; // 320 KiB
                var task = new Microsoft.Graph.Upload.UploadSessionLargeFileUploadTask<DriveItem>(session, stream, chunkSize);
                var result = await task.UploadAsync(ct);

                if (result.UploadSucceeded && result.ItemResponse is not null)
                {
                    var item = result.ItemResponse;
                    return Ok(new { id = item.Id, name = item.Name, webUrl = item.WebUrl, size = item.Size });
                }

                return Problem("Upload failed.");
            }
        }
    }
}
