﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Blazorise.Bootstrap" Version="1.8.1" />
    <PackageReference Include="Blazorise.SignaturePad" Version="1.8.1" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.19" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.19" PrivateAssets="all" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Shared\JSWaiverCheckIn.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\js\" />
  </ItemGroup>

</Project>
