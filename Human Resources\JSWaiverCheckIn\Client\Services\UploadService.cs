using System.Text;
using Microsoft.Extensions.Configuration;

namespace JSWaiverCheckIn.Client.Services
{
    public class UploadService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;

        public UploadService(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _configuration = configuration;
        }

        public async Task<string?> UploadScreenCaptureToSharePoint(string base64Data, string candidateName, DateTime timestamp)
        {
            try
            {
                Console.WriteLine("Starting SharePoint upload process...");

                // Get SharePoint configuration
                var driveId = _configuration["SharePoint:DriveId"];
                var folderId = _configuration["SharePoint:FolderId"];

                Console.WriteLine($"DriveId: {driveId}");
                Console.WriteLine($"FolderId: {folderId}");

                if (string.IsNullOrEmpty(driveId) || string.IsNullOrEmpty(folderId))
                {
                    Console.WriteLine("SharePoint configuration missing - DriveId or FolderId not found");
                    return null;
                }

                // Generate filename
                var fileName = $"CheckIn_{candidateName}_{timestamp:yyyyMMdd_HHmmss}.png";
                fileName = SanitizeFileName(fileName);
                Console.WriteLine($"Generated filename: {fileName}");

                // Convert base64 to bytes
                var base64Content = base64Data.Contains(",")
                    ? base64Data.Substring(base64Data.IndexOf(",") + 1)
                    : base64Data;

                var fileBytes = Convert.FromBase64String(base64Content);
                Console.WriteLine($"File size: {fileBytes.Length} bytes");

                // Create multipart form data
                using var content = new MultipartFormDataContent();
                using var fileContent = new ByteArrayContent(fileBytes);
                fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("image/png");
                content.Add(fileContent, "file", fileName);

                // Upload to SharePoint via Graph API
                var uploadUrl = $"api/graph/upload?driveId={driveId}&parentItemId={folderId}&fileName={fileName}";
                Console.WriteLine($"Upload URL: {uploadUrl}");

                var response = await _httpClient.PostAsync(uploadUrl, content);
                Console.WriteLine($"Response status: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<UploadResult>();
                    Console.WriteLine($"File uploaded successfully: {result?.WebUrl}");
                    return result?.WebUrl;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Upload failed: {response.StatusCode} - {errorContent}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error uploading to SharePoint: {ex.Message}");
                return null;
            }
        }

        private static string SanitizeFileName(string fileName)
        {
            // Remove invalid characters for SharePoint file names
            var invalidChars = new char[] { '/', '\\', ':', '*', '?', '"', '<', '>', '|', '#', '%' };
            foreach (var invalidChar in invalidChars)
            {
                fileName = fileName.Replace(invalidChar, '_');
            }
            return fileName;
        }

        public class UploadResult
        {
            public string? Id { get; set; }
            public string? Name { get; set; }
            public string? WebUrl { get; set; }
            public long? Size { get; set; }
        }
    }
}
