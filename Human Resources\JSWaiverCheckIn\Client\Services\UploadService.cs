using System.Net.Http.Json;

namespace JSWaiverCheckIn.Client.Services
{
    public class UploadService
    {
        private readonly HttpClient _httpClient;

        public UploadService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<string?> UploadScreenCaptureToSharePoint(string base64Data, string candidateName, DateTime timestamp)
        {
            try
            {
                Console.WriteLine("Starting SharePoint upload process...");

                // Create request object
                var request = new ScreenCaptureRequest
                {
                    Base64Data = base64Data,
                    CandidateName = candidateName,
                    Timestamp = timestamp
                };

                // Send to server endpoint
                var response = await _httpClient.PostAsJsonAsync("api/graph/upload-screen-capture", request);
                Console.WriteLine($"Response status: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var result = await response.Content.ReadFromJsonAsync<UploadResult>();
                    Console.WriteLine($"File uploaded successfully: {result?.WebUrl}");
                    return result?.WebUrl;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Upload failed: {response.StatusCode} - {errorContent}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error uploading to SharePoint: {ex.Message}");
                return null;
            }
        }

        public class ScreenCaptureRequest
        {
            public string Base64Data { get; set; } = string.Empty;
            public string CandidateName { get; set; } = string.Empty;
            public DateTime Timestamp { get; set; }
        }

        public class UploadResult
        {
            public string? Id { get; set; }
            public string? Name { get; set; }
            public string? WebUrl { get; set; }
            public long? Size { get; set; }
        }
    }
}
