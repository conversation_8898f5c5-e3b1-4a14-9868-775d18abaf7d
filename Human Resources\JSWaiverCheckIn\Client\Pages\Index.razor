﻿@page "/"
@inject NavigationManager Navigation

<PageTitle>Job Shadow Waiver Check-In</PageTitle>

<div class="thank-you-container">
    <div class="thank-you-content">
        <div class="success-icon">
            <img src="stc-blacklogo-2024.png" alt="Company Logo" class="img-fluid" width="200" />
        </div>

        <h3 class="thank-you-title mt-4">Job Shadow Waiver Check-In</h3>

        <div class="thank-you-message mt-4">
            <p>Welcome! Please review and acknowledge the following terms before beginning your job shadow.</p>
            <p>
                To continue, you must accept all terms and fill in all required information in the waiver form.
            </p>
        </div>

        <div class="thank-you-actions">
            <button type="button" class="btn btn-primary btn-lg" @onclick="CheckIn">
                START
            </button>
        </div>
    </div>
</div>
@* <div class="landing-page">
    <div class="logo-container">
        
    </div>

    <div class="title-container form-card">
        <h1 class="main-title">Job Shadow Waiver Check-In</h1>
        <p class="mt-3">
            Welcome! Please review and acknowledge the following terms before beginning your job shadow.
            To continue, you must accept all terms and fill in all required information in the waiver form.
        </p>
        <div class="button-container">
            <button class="btn btn-primary shadow check-in-button" @onclick="CheckIn">START</button>
        </div>
    </div>


</div> *@

@code {
    private void CheckIn()
    {
        Navigation.NavigateTo("/checkin");
    }
}