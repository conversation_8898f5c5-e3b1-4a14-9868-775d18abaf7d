.thank-you-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1f1f1f 0%, #0a0a0a 100%);
    padding: 20px;
}

.thank-you-content {
    background: white;
    border-radius: 20px;
    padding: 60px 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
}

.success-icon {
    margin-bottom: 30px;
    display: flex;
    justify-content: center;
}

.thank-you-title {
    color: #333;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.thank-you-message {
    margin-bottom: 40px;
}

.thank-you-message p {
    color: #666;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 15px;
}

.thank-you-actions {
    margin-top: 30px;
}

.btn-lg {
    padding: 15px 40px;
    font-size: 1.2rem;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-lg:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

@media (max-width: 768px) {
    .thank-you-content {
        padding: 40px 20px;
    }
    
    .thank-you-title {
        font-size: 2rem;
    }
    
    .thank-you-message p {
        font-size: 1rem;
    }
}
