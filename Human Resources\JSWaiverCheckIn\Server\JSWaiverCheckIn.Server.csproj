<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.15.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.19" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="8.0.19" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageReference Include="Microsoft.Graph" Version="5.91.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Client\JSWaiverCheckIn.Client.csproj" />
    <ProjectReference Include="..\Shared\JSWaiverCheckIn.Shared.csproj" />
  </ItemGroup>

</Project>
