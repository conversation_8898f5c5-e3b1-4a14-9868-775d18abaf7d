@page "/thankyou"
@inject NavigationManager Navigation

<PageTitle>Thank You - Check-In Complete</PageTitle>

<div class="thank-you-container">
    <div class="thank-you-content">
        <div class="success-icon">
            <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="#28a745" stroke-width="2" fill="#28a745" fill-opacity="0.1"/>
                <path d="m9 12 2 2 4-4" stroke="#28a745" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </div>
        
        <h1 class="thank-you-title">Thank You!</h1>
        
        <div class="thank-you-message">
            <p>Your check-in has been completed successfully.</p>
            <p>All required information has been submitted and your documents have been uploaded to our system.</p>
        </div>
        
        <div class="thank-you-actions">
            <button type="button" class="btn btn-primary btn-lg" @onclick="GoToStart">
                Return to Start
            </button>
        </div>
    </div>
</div>



@code {
    private void GoToStart()
    {
        Navigation.NavigateTo("/", true);
    }
}
